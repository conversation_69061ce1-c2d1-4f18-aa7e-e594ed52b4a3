import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { BaseCrudService } from 'src/app/shared/services';

@Injectable({
  providedIn: 'root'
})
export class PaymentService extends BaseCrudService {
  private readonly paymentSource = new BehaviorSubject<any>(null); // initial value: null
  userCardDetails$ = this.paymentSource.asObservable();

  private readonly showBtnLoaderSource = new BehaviorSubject<boolean>(false);
  showBtnLoader$ = this.showBtnLoaderSource.asObservable();

  private readonly isAddressIncompleteSource = new BehaviorSubject<boolean>(false);
  isAddressIncomplete$ = this.isAddressIncompleteSource.asObservable();

  setUserPayment(user: any) {
    this.paymentSource.next(user);
  }
  
  getBaseAPIPath(): string {
    return `${API_URL.services}/${API_URL.app}/${API_URL.payment.root}`;
  }

  deleteCard(id: string, userId: number) {
   return this.httpClient.delete(`${API_URL.services}/${API_URL.app}/${API_URL.payment.root}/${API_URL.payment.deleteNMICustomer}?customerVaultId=${id}&userId=${userId}`);
  }

  showBtnLoader(show: boolean) {
    this.showBtnLoaderSource.next(show);
  }

  isAddressIncomplete(isComplete: boolean) {
    this.isAddressIncompleteSource.next(isComplete);
  }
}
