<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isViewStudentSideNavOpen || isViewInstructorSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isViewStudentSideNavOpen) {
      <app-view-student
        [selectedStudentDetails]="selectedStudentInfo"
        [isFromAttendance]="true"
        (closeViewSideNav)="toggleStudentDetail(false, null)"></app-view-student>
    }
    @if (isViewInstructorSideNavOpen) {
      <app-view-instructor
        [selectedInstructorViewDetails]="selectedInstructorInfo"
        (closeViewSideNav)="toggleInstructorDetail(false, null)"></app-view-instructor>
    }
  </mat-sidenav>
</mat-sidenav-container>

<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : attendanceTemplate"></ng-container>

<ng-template #attendanceTemplate>
  <div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
      <div class="title">{{ getScheduleDetailDescription(selectedScheduleDetail) | titlecase }} Attendance</div>
      <div class="action-btn-wrapper">
        <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">
          Close
        </button>
      </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
      <div class="attendance-wrapper">
        <div class="attendance-header-wrapper">
          <div class="attendance-header">
            <div class="name">
              @if (selectedScheduleDetail?.isCancelSchedule) {
                @if (selectedScheduleDetail?.isLateCancelSchedule) {
                  <span>Late Canceled: </span>
                } @else {
                  <span>Canceled: </span>
                }
              }
              {{ getScheduleDetailDescription(selectedScheduleDetail) | titlecase }}
            </div>
            <div class="class-info-wrapper">
              <div>{{ schedulerService.getClassType(selectedScheduleDetail?.classType) }}</div>
              <div class="dot"></div>
              <div>{{ schedulerService.getLessonType(selectedScheduleDetail?.lessonType) }}</div>
            </div>
          </div>
          <div class="attendance-content-wrapper detail">
            <div class="attendance-content">
              <img [src]="constants.staticImages.icons.timeCircleClock" class="img-icon" alt="" />
              <div class="info-content">
                <span
                  >{{ selectedScheduleDetail?.start | localDate | date: constants.dateFormats.hh_mm_a }} -
                  {{ selectedScheduleDetail?.end | localDate | date: constants.dateFormats.hh_mm_a }} -
                  {{ selectedScheduleDetail?.scheduleDate | localDate | date: constants.fullDate }}</span
                >
              </div>
            </div>
            <div class="dot"></div>
            <div class="attendance-content">
              <img [src]="constants.staticImages.icons.profileCircle" class="img-icon" alt="" />
              <div class="info-content">
                <span>
                  @if (selectedScheduleDetail?.classType === classTypes.ENSEMBLE_CLASS) {
                    @for (assignedInstructors of selectedScheduleDetail?.assignedInstructors; track $index) {
                    <ng-container *ngIf="$index < 1">
                       <span class="pointer" (click)="toggleInstructorDetail(true, assignedInstructors.instructorId ?? null)">{{ assignedInstructors?.name }}</span> 
                      <!-- <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div> -->
                    </ng-container>
                    } @if (selectedScheduleDetail?.assignedInstructors!.length>1) {
                    <div class="remaining-instrument-available-count" 
                    [matTooltip]="getInstructorNames(selectedScheduleDetail!.assignedInstructors)">
                      {{ selectedScheduleDetail!.assignedInstructors!.length - 1}}+
                    </div>
                    } } @else {
                     <span class="pointer" (click)="toggleInstructorDetail(true, selectedScheduleDetail?.instructorId ?? null)">{{ selectedScheduleDetail?.instructorName}}</span> 
                    }
                </span>
              </div>
            </div>
          </div>
          <div class="attendance-content-wrapper detail">
            <div class="attendance-content">
              <img [src]="constants.staticImages.icons.location" class="img-icon" alt="" />
              <div class="info-content">
                <span>{{ selectedScheduleDetail?.locationName }}</span>
              </div>
            </div>
            @if (selectedScheduleDetail?.roomName) {
              <div class="dot"></div>
              <div class="attendance-content">
                <img [src]="constants.staticImages.icons.roomIcon" class="img-icon" alt="" />
                <div class="info-content">
                  <span>{{ selectedScheduleDetail?.roomName }}</span>
                </div>
              </div>
            }
          </div>
          <div class="attendance-content-wrapper">
            <div class="attendance-content">
              <img [src]="constants.staticImages.icons.checkCircle" class="img-icon" alt="" />
              <div class="info-title">Canceled By</div>
              <div class="info-content">
                <span>{{ selectedScheduleDetail?.cancelByName }} - {{ selectedScheduleDetail?.cancelOn | localDate | date: 'mediumDate' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="register-student-wrapper">
          <div class="title">Registered Clients</div>
          <ng-container
            [ngTemplateOutlet]="
              selectedScheduleDetail?.studentDetails?.length ? studentDetailCard : noDataFound
            "></ng-container>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #studentDetailCard>
  @for (student of selectedScheduleDetail?.studentDetails; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body">
        <div class="student-name-photo-wrapper" (click)="toggleStudentDetail(true, student.studentId)">
          <div class="placeholder-name">
            {{ getInitials(student.studentName) | uppercase }}
          </div>
          <div class="student-info">
            <div>{{ student.studentName | titlecase }}</div>
            <div class="student-info-content">
              <div class="info-content text-gray">Age: {{ student.studentAge }}</div>
              @if (!selectedScheduleDetail?.campName) {
                <div class="dot"></div>
                <div class="info-content text-gray">Skill: {{ selectedScheduleDetail?.skillType }}</div>
                <div class="dot"></div>
                <div class="info-content text-black">
                  Grade: <span class="primary-color ms-1">{{ student.grade }}</span>
                  <mat-icon>arrow_upward</mat-icon>
                </div>
              }
            </div>
          </div>
        </div>
        <!-- to be used -->
        <div class="mark-attendance" *ngIf="!selectedScheduleDetail?.isCancelSchedule">
           @if (
            !selectedScheduleDetail?.isDraftSchedule &&
            !selectedScheduleDetail?.isCancelSchedule &&
            !this.isFutureEvent(this.selectedScheduleDetail?.scheduleDate | localDate)
          ) {
            <div class="icons">
              <img
                [ngClass]="{
                  'no-show-img': student.isPresent === false
                }"
                matTooltip="No Show" alt=""
                [src]="constants.staticImages.icons.noShowBadge"
                class="absent me-3"
                (click)="markAttendance(false, student)" />
              <img
                [ngClass]="{
                  'present-img': student.isPresent
                }"
                matTooltip="Present" alt=""
                [src]="constants.staticImages.icons.checkCircle"
                class="present"
                (click)="markAttendance(true, student)" />
            </div>
          }
        </div>
      </div>
    </div>
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h5 class="mb-0">NO CLIENT REGISTERED!</h5>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
