<div class="header-tab-with-btn">
  <div class="back-btn-wrapper" (click)="goBack()">
    <mat-icon>keyboard_arrow_left</mat-icon>
    <span class="page-title">
      @if (viewScheduleContent?.classType === classTypes.SUMMER_CAMP) {
        <span class="camp-name"
          >{{ viewScheduleContent?.campName | titlecase }} ({{
            schedulerService.getNumberOfDays(viewScheduleContent!.campStartDate, viewScheduleContent!.campEndDate)
          }}d)</span
        >
      } @else {
        @switch (viewScheduleContent?.classType) {
          @case (classTypes.GROUP_CLASS) {
            {{ viewScheduleContent?.groupClassName | titlecase }}
          }
          @case (classTypes.ENSEMBLE_CLASS) {
            {{ viewScheduleContent?.ensembleClassName | titlecase }}
          }
          @case (classTypes.MAKE_UP) {
            {{ viewScheduleContent?.instrumentName }} Make-Up Lesson
          }
          @case (classTypes.INTRODUCTORY) {
            Introductory {{ viewScheduleContent?.instrumentName }} Lesson
          }
          @default {
            {{ viewScheduleContent?.instrumentName }} Lesson
          }
        }
        <span *ngIf="viewScheduleContent?.start">
          ({{ getTimeDiff(viewScheduleContent!.start, viewScheduleContent!.end) }})
        </span>
      }
    </span>
  </div>
</div>

<div class="auth-page-wrapper auth-page-with-header">
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewSchedule"></ng-container>
</div>

<ng-template #viewSchedule>
  <div class="schedule-title">Lesson Details</div>
  <div class="o-card">
    <div class="schedule-detail align-items-baseline">
      <div>
        <div class="o-card-title">
          @if (viewScheduleContent?.classType === classTypes.SUMMER_CAMP) {
            <span class="camp-name"
              >{{ viewScheduleContent?.campName | titlecase }} ({{
                schedulerService.getNumberOfDays(viewScheduleContent!.campStartDate, viewScheduleContent!.campEndDate)
              }}d)</span
            >
          } @else {
            @switch (viewScheduleContent?.classType) {
              @case (classTypes.GROUP_CLASS) {
                {{ viewScheduleContent?.groupClassName | titlecase }}
              }
              @case (classTypes.ENSEMBLE_CLASS) {
                {{ viewScheduleContent?.ensembleClassName | titlecase }}
              }
              @case (classTypes.MAKE_UP) {
                {{ viewScheduleContent?.instrumentName }} Make-Up Lesson
              }
              @case (classTypes.INTRODUCTORY) {
                Introductory {{ viewScheduleContent?.instrumentName }} Lesson
              }
              @default {
                {{ viewScheduleContent?.instrumentName }} Lesson
              }
            }
            <span *ngIf="viewScheduleContent?.start">
              ({{ getTimeDiff(viewScheduleContent!.start, viewScheduleContent!.end) }})
            </span>
          }
        </div>
        <div class="o-card-body">
          <div class="schedule-content">
            <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
            {{ viewScheduleContent?.start | localDate | date: "shortTime" }}-{{ viewScheduleContent?.end | localDate | date: "shortTime" }} -
            {{ viewScheduleContent?.scheduleDate | localDate | date: constants.fullDate }}
          </div>
          <div class="dot"></div>
          <div class="schedule-content">
            <img [src]="constants.staticImages.icons.location" alt="" />
            {{ viewScheduleContent?.locationName }}
          </div>
          @if (viewScheduleContent?.roomName) {
            <div class="dot"></div>
            <div class="schedule-content">
              <img [src]="constants.staticImages.icons.roomIcon" alt="" />
              {{ viewScheduleContent?.roomName }}
            </div>
          }
        </div>
      </div>
      <div class="class-lesson-type">
        {{ schedulerService.getClassType(viewScheduleContent?.classType) }}
        <div class="dot"></div>
        {{ schedulerService.getLessonType(viewScheduleContent?.lessonType) }}
      </div>
    </div>
  </div>

  <div class="schedule-title">Instructor</div>
  <div class="o-card" *ngIf="viewScheduleContent?.classType !== classTypes.ENSEMBLE_CLASS">
    @if (viewScheduleContent?.instructorProfilePhoto) {
      <img [src]="viewScheduleContent?.instructorProfilePhoto" alt="" />
    } @else {
      <div class="placeholder-name">
        <div>
          {{ getInitials(viewScheduleContent?.instructorName) | uppercase }}
        </div>
      </div>
    }
    <div class="schedule-detail">
      <div class="pointer" (click)="openInstructorDetails(viewScheduleContent?.instructorId ?? 0)">
        <div class="o-card-title">{{ viewScheduleContent?.instructorName | titlecase }}</div>
        <div class="o-card-body">
          @if (viewScheduleContent?.instructorInstrument?.length) {
            <div class="schedule-content">
              <span class="text-gray me-1">Instrument:</span>
              {{ viewScheduleContent?.instrumentName }}
              <span class="primary-color ms-1">{{ viewScheduleContent!.instructorInstrument[0].instrumentGrade }}</span>
            </div>
            <div class="dot"></div>
          }
          <div class="schedule-content">
            <img [src]="constants.staticImages.icons.phone" alt="" />
            {{ viewScheduleContent?.instructorPhoneNo }}
          </div>
          <div class="dot"></div>
          <div class="schedule-content">
            <img [src]="constants.staticImages.icons.email" alt="" />
            {{ viewScheduleContent?.instructorEmail }}
          </div>
        </div>
      </div>
      <!-- To be used -->
      <!-- <div class="arrival-time"><img [src]="constants.staticImages.icons.timeCircleClock" alt="" /> 5 min Late</div> -->
    </div>
  </div>

  <ng-container *ngIf="viewScheduleContent?.classType == classTypes.ENSEMBLE_CLASS">
    @for (assignedInstructor of viewScheduleContent?.assignedInstructors; track $index) {
    <div class="o-card" >
      @if (viewScheduleContent?.instructorProfilePhoto) {
        <img [src]="viewScheduleContent?.instructorProfilePhoto" alt="" />
      } @else {
        <div class="placeholder-name">
          <div>
            {{ getInitials(assignedInstructor?.name) | uppercase }}
          </div>
        </div>
      }
      <div class="schedule-detail">
        <div class="pointer" (click)="openInstructorDetails(assignedInstructor?.id ?? 0)">
          <div class="o-card-title">{{ assignedInstructor.name | titlecase }}</div>
          <div class="o-card-body">
            @if (assignedInstructor.instruments.length) {
              <div class="schedule-content">
                <span class="text-gray me-1">Instruments:</span>
                @for (instruments of assignedInstructor.instruments; track $index) {
                  <ng-container *ngIf="$index < 2">

                    {{ instruments.instrumentName }}
        
                  <span class="primary-color ms-1">{{ instruments.instrumentGrade}}</span>
                  <div class="dot" *ngIf="!$last"></div>
                  </ng-container>
                }
                @if (assignedInstructor.instruments.length > 2) {
                  <div class="remaining-instrument-available-count" [matTooltip]="getInstrumentNames(assignedInstructor.instruments)">
                    {{ assignedInstructor.instruments.length - 2 }}+
                  </div>
                  }
              </div>
              <div class="dot"></div>
            }
            <div class="schedule-content">
              <img [src]="constants.staticImages.icons.phone" alt="" />
              {{ assignedInstructor?.phoneNumber }}
            </div>
            <div class="dot"></div>
            <div class="schedule-content">
              <img [src]="constants.staticImages.icons.email" alt="" />
              {{ assignedInstructor?.email }}
            </div>
          </div>
        </div>
        <!-- To be used -->
        <!-- <div class="arrival-time"><img [src]="constants.staticImages.icons.timeCircleClock" alt="" /> 5 min Late</div> -->
      </div>
    </div>
  }
  </ng-container>

  <div class="schedule-title">
    Register Clients
    <span class="primary-color">
      {{ viewScheduleContent?.studentDetails?.length }}/{{ viewScheduleContent?.studentCapacity }}
    </span>
  </div>
  <ng-container
    [ngTemplateOutlet]="
      viewScheduleContent?.studentDetails?.length ? registeredStudents : noRegisteredStudent
    "></ng-container>
</ng-template>

<ng-template #registeredStudents>
  @for (studentDetail of viewScheduleContent?.studentDetails; track $index) {
    <div class="o-card">
      <div class="placeholder-name">
        <div>
          {{ getInitials(studentDetail.studentName) | uppercase }}
        </div>
      </div>
      <div class="schedule-detail">
        <div class="pointer" (click)="openStudentDetails(studentDetail.studentId)">
          <div class="o-card-title">{{ studentDetail.studentName | titlecase }}</div>
          <div class="o-card-body">
            <div class="schedule-content text-gray">Age: {{ studentDetail.studentAge }}</div>
            @if (!viewScheduleContent?.campName) {
              <div class="dot"></div>
              <div class="schedule-content text-gray">Skill: {{ viewScheduleContent?.skillType }}</div>
              @if (studentDetail.grade) {
                <div class="dot"></div>
                <div class="schedule-content">
                  Grade <span class="primary-color ms-1">{{ studentDetail?.grade }}</span>
                  <mat-icon>arrow_upward</mat-icon>
                </div>
              }
            }
            <div class="dot"></div>
            <div class="schedule-content">
              <img [src]="constants.staticImages.icons.phone" alt="" />
              <div class="text-gray me-2">{{ studentDetail.accountManagerPhoneNo }}</div>
            </div>
            <div class="dot"></div>
            <div class="schedule-content">
              <img [src]="constants.staticImages.icons.email" alt="" />
              <div class="text-gray me-1">{{ studentDetail.accountManagerEmail }}</div>
            </div>
          </div>
        </div>
        <div class="student-details">
          @if (
            !viewScheduleContent?.isDraftSchedule &&
            !viewScheduleContent?.isCancelSchedule &&
            !this.isFutureEvent(this.viewScheduleContent?.scheduleDate)
          ) {
            <div class="icons">
              <img
                [ngClass]="{
                  'no-show-img': studentDetail.isPresent === false
                }"
                [src]="constants.staticImages.icons.noShowBadge"
                matTooltip="No Show" alt=""
                class="absent me-3"
                (click)="markAttendance(false, studentDetail)" />
              <img
                [ngClass]="{
                  'present-img': studentDetail.isPresent
                }"
                matTooltip="Present"
                [src]="constants.staticImages.icons.checkCircle"
                class="present" alt=""
                (click)="markAttendance(true, studentDetail)" />
            </div>
          }
        </div>
      </div>
    </div>
  }
</ng-template>

<ng-template #noRegisteredStudent>
  <div class="o-card justify-content-center">
    <div class="o-card-body">
      <div class="schedule-content">No Clients Registered</div>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
